# Master-Know 前端开发完整方案

## 🎯 目标
为Master-Know项目创建现代化的React TypeScript前端，集成聊天和搜索功能。

## 📋 技术栈选择

### 核心技术栈
- **React 18** + **TypeScript** - 现代React开发
- **Vite** - 快速构建工具（替代Webpack）
- **Chakra UI v2.8.2** - 稳定的UI组件库
- **TanStack Router** - 类型安全的路由
- **TanStack Query** - 数据获取和状态管理
- **Axios** - HTTP客户端

### 为什么选择这个技术栈？

1. **Chakra UI v2 vs v3**：
   - v3是重大重写，API变化很大，迁移成本高
   - v2更稳定，生产环境验证充分
   - v2有更好的文档和社区支持

2. **Vite vs Create React App**：
   - Vite启动速度更快（冷启动 < 1秒）
   - 更好的TypeScript支持
   - 现代化的构建工具

## 🚀 实施步骤

### 第一步：环境准备
```bash
# 1. 回退到干净状态
cd frontend
git checkout -- package.json package-lock.json
rm -rf node_modules

# 2. 安装正确的依赖版本
npm install @chakra-ui/react@^2.8.2 @chakra-ui/icons@^2.1.1
npm install @emotion/react@^11.11.0 @emotion/styled@^11.11.0
npm install framer-motion@^10.16.16

# 3. 验证安装
npm list @chakra-ui/react
```

### 第二步：创建核心组件
```bash
# 创建组件目录结构
mkdir -p src/components/Chat
mkdir -p src/components/Search
mkdir -p src/components/Dashboard
```

### 第三步：组件开发优先级

#### 优先级1：核心功能组件
1. **ChatInterface** - AI聊天界面
   - 对话列表
   - 消息发送/接收
   - 实时更新

2. **SearchInterface** - 文档搜索界面
   - 全文搜索
   - 语义搜索
   - 结果展示

3. **Dashboard** - 仪表板
   - 欢迎页面
   - 快速操作
   - 统计信息

#### 优先级2：支持组件
1. **Navigation** - 导航组件
2. **Layout** - 布局组件
3. **Common** - 通用组件

### 第四步：API集成
```bash
# 重新生成API客户端
curl -o openapi.json http://localhost:8000/api/v1/openapi.json
npm run generate-client
```

### 第五步：开发和测试
```bash
# 启动开发服务器
npm run dev

# 运行类型检查
npm run build

# 运行测试
npm test
```

## 🔧 关键配置文件

### vite.config.ts
```typescript
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react-swc'
import { TanStackRouterVite } from '@tanstack/router-vite-plugin'

export default defineConfig({
  plugins: [
    react(),
    TanStackRouterVite()
  ],
  resolve: {
    alias: {
      '@': '/src'
    }
  },
  server: {
    port: 5173,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true
      }
    }
  }
})
```

### tsconfig.json 关键配置
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true
  }
}
```

## 🎨 UI组件设计原则

### Chakra UI v2 最佳实践
1. **使用正确的API**：
   ```tsx
   // ✅ 正确 - v2 API
   <Button leftIcon={<FiPlus />} colorScheme="blue">
     Add Item
   </Button>
   
   // ❌ 错误 - v3 API
   <Button colorPalette="blue">
     <FiPlus />
     Add Item
   </Button>
   ```

2. **Toast使用**：
   ```tsx
   const toast = useToast()
   
   toast({
     title: "Success",
     description: "Operation completed",
     status: "success",
     duration: 3000,
     isClosable: true
   })
   ```

3. **响应式设计**：
   ```tsx
   <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={4}>
     {/* 内容 */}
   </SimpleGrid>
   ```

## 🧪 测试策略

### 单元测试
- 组件渲染测试
- 用户交互测试
- API调用测试

### 集成测试
- 页面流程测试
- API集成测试
- 路由测试

### E2E测试
- 完整用户流程
- 跨浏览器测试

## 📦 构建和部署

### 开发构建
```bash
npm run dev
```

### 生产构建
```bash
npm run build
npm run preview
```

### Docker构建
```bash
docker build -t master-know-frontend:latest .
```

## 🔍 故障排除

### 常见问题
1. **TypeScript错误**：确保使用正确的Chakra UI v2 API
2. **构建失败**：检查依赖版本兼容性
3. **API调用失败**：验证后端服务状态

### 调试工具
- React DevTools
- TanStack Query DevTools
- Vite DevTools

## 📝 下一步行动计划

1. **立即执行**：
   - 回退到干净状态
   - 安装正确的依赖版本
   - 创建基础组件结构

2. **短期目标**（1-2天）：
   - 完成ChatInterface组件
   - 完成SearchInterface组件
   - 基础路由配置

3. **中期目标**（3-5天）：
   - 完善UI交互
   - 添加错误处理
   - 优化性能

4. **长期目标**（1-2周）：
   - 完整测试覆盖
   - 生产环境优化
   - 文档完善

## 🎯 成功指标

- [ ] 所有组件正常渲染
- [ ] API调用成功
- [ ] TypeScript编译无错误
- [ ] 生产构建成功
- [ ] 基本功能测试通过
- [ ] 响应式设计正常
- [ ] 性能指标达标

---

**准备好开始新的对话窗口，按照这个计划执行前端开发！**
