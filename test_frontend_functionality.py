#!/usr/bin/env python3
"""
Master-Know 前端功能测试脚本
测试新开发的聊天和搜索功能
"""

import requests
import json
import time
from typing import Dict, Any

class FrontendTester:
    def __init__(self):
        self.backend_url = "http://localhost:8000/api/v1"
        self.frontend_url = "http://localhost:5173"
        self.token = None
        self.user_id = None
        
    def login(self) -> bool:
        """登录获取token"""
        try:
            response = requests.post(
                f"{self.backend_url}/login/access-token",
                data={
                    "username": "<EMAIL>",
                    "password": "changethis"
                },
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )
            
            if response.status_code == 200:
                data = response.json()
                self.token = data["access_token"]
                print("✅ 登录成功")
                return True
            else:
                print(f"❌ 登录失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 登录异常: {e}")
            return False
    
    def get_headers(self) -> Dict[str, str]:
        """获取认证头"""
        return {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json"
        }
    
    def test_frontend_accessibility(self) -> bool:
        """测试前端页面可访问性"""
        print("\n🔍 测试前端页面可访问性...")
        
        try:
            # 测试主页
            response = requests.get(self.frontend_url, timeout=5)
            if response.status_code == 200:
                print("✅ 前端主页可访问")
            else:
                print(f"❌ 前端主页访问失败: {response.status_code}")
                return False
                
            return True
            
        except Exception as e:
            print(f"❌ 前端访问异常: {e}")
            return False
    
    def test_conversation_api(self) -> bool:
        """测试对话API功能"""
        print("\n💬 测试对话API功能...")
        
        try:
            # 创建新对话
            response = requests.post(
                f"{self.backend_url}/conversations/",
                headers=self.get_headers(),
                json={"title": "Frontend Test Chat"}
            )
            
            if response.status_code == 200:
                conversation = response.json()
                conversation_id = conversation["id"]
                print(f"✅ 创建对话成功: {conversation_id}")
                
                # 发送消息
                response = requests.post(
                    f"{self.backend_url}/conversations/chat",
                    headers=self.get_headers(),
                    json={
                        "conversation_id": conversation_id,
                        "message": "Hello, this is a test message from frontend"
                    }
                )
                
                if response.status_code == 200:
                    print("✅ 发送消息成功")
                    
                    # 获取对话消息
                    response = requests.get(
                        f"{self.backend_url}/conversations/{conversation_id}/messages",
                        headers=self.get_headers()
                    )
                    
                    if response.status_code == 200:
                        messages = response.json()
                        print(f"✅ 获取消息成功，共 {len(messages.get('data', []))} 条消息")
                        return True
                    else:
                        print(f"❌ 获取消息失败: {response.status_code}")
                        return False
                else:
                    print(f"❌ 发送消息失败: {response.status_code}")
                    return False
            else:
                print(f"❌ 创建对话失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 对话API测试异常: {e}")
            return False
    
    def test_search_api(self) -> bool:
        """测试搜索API功能"""
        print("\n🔍 测试搜索API功能...")
        
        try:
            # 测试全文搜索
            response = requests.post(
                f"{self.backend_url}/search/documents",
                headers=self.get_headers(),
                json={"query": "test"}
            )
            
            if response.status_code == 200:
                results = response.json()
                print(f"✅ 全文搜索成功，找到 {len(results.get('results', []))} 个结果")
            else:
                print(f"⚠️ 全文搜索返回: {response.status_code}")
            
            # 测试语义搜索
            response = requests.post(
                f"{self.backend_url}/search/documents/semantic",
                headers=self.get_headers(),
                json={"query": "test", "limit": 5}
            )
            
            if response.status_code == 200:
                results = response.json()
                print(f"✅ 语义搜索成功，找到 {len(results.get('results', []))} 个结果")
            else:
                print(f"⚠️ 语义搜索返回: {response.status_code}")
            
            # 测试搜索统计
            response = requests.get(
                f"{self.backend_url}/search/stats",
                headers=self.get_headers()
            )
            
            if response.status_code == 200:
                stats = response.json()
                print(f"✅ 搜索统计成功: {stats}")
                return True
            else:
                print(f"⚠️ 搜索统计返回: {response.status_code}")
                return True  # 搜索功能基本可用
                
        except Exception as e:
            print(f"❌ 搜索API测试异常: {e}")
            return False
    
    def test_user_api(self) -> bool:
        """测试用户API功能"""
        print("\n👤 测试用户API功能...")
        
        try:
            # 获取当前用户信息
            response = requests.get(
                f"{self.backend_url}/users/me",
                headers=self.get_headers()
            )
            
            if response.status_code == 200:
                user = response.json()
                self.user_id = user["id"]
                print(f"✅ 获取用户信息成功: {user['email']}")
                return True
            else:
                print(f"❌ 获取用户信息失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 用户API测试异常: {e}")
            return False
    
    def run_all_tests(self) -> bool:
        """运行所有测试"""
        print("🚀 开始前端功能测试...")
        print("=" * 50)
        
        # 登录
        if not self.login():
            return False
        
        # 测试各个功能
        tests = [
            ("前端可访问性", self.test_frontend_accessibility),
            ("用户API", self.test_user_api),
            ("对话API", self.test_conversation_api),
            ("搜索API", self.test_search_api),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed += 1
                    print(f"✅ {test_name} 测试通过")
                else:
                    print(f"❌ {test_name} 测试失败")
            except Exception as e:
                print(f"❌ {test_name} 测试异常: {e}")
        
        print("\n" + "=" * 50)
        print(f"📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！前端功能正常")
            return True
        else:
            print("⚠️ 部分测试失败，请检查相关功能")
            return False

def main():
    """主函数"""
    tester = FrontendTester()
    
    print("Master-Know 前端功能测试")
    print("测试目标:")
    print("- 前端页面可访问性")
    print("- 聊天功能API")
    print("- 搜索功能API")
    print("- 用户认证API")
    print()
    
    success = tester.run_all_tests()
    
    if success:
        print("\n🎯 建议下一步:")
        print("1. 打开浏览器访问 http://localhost:5173")
        print("2. 使用 <EMAIL> / changethis 登录")
        print("3. 测试聊天功能 (/chat)")
        print("4. 测试搜索功能 (/search)")
        print("5. 检查仪表板统计信息")
    else:
        print("\n🔧 需要修复的问题:")
        print("1. 检查后端服务是否正常运行")
        print("2. 检查前端开发服务器是否启动")
        print("3. 检查API端点是否正确配置")
    
    return success

if __name__ == "__main__":
    main()
