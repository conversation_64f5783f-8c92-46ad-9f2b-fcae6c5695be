import { createFileRoute, Link } from '@tanstack/react-router'
import { useState } from 'react'
import {
  Box,
  VStack,
  HStack,
  Input,
  Button,
  Text,
  Flex,
  useToast,
  Spinner,
  Badge,
  Select,
  Divider,
  Container,
  Heading,
  IconButton,
} from '@chakra-ui/react'
import { FiSearch, FiExternalLink, FiFile, FiHome } from 'react-icons/fi'
import { useMutation } from '@tanstack/react-query'

// 模拟搜索结果
const mockSearchResults = [
  {
    id: '1',
    title: 'React 组件最佳实践',
    content: 'React 组件是构建用户界面的基本单元。在编写组件时，应该遵循单一职责原则，确保每个组件只负责一个功能...',
    score: 0.95,
    document_id: 'doc_1',
    chunk_index: 0,
    metadata: { category: 'frontend', language: 'javascript' },
  },
  {
    id: '2',
    title: 'TypeScript 类型系统',
    content: 'TypeScript 提供了强大的类型系统，可以帮助开发者在编译时发现错误。类型注解是 TypeScript 的核心特性...',
    score: 0.87,
    document_id: 'doc_2',
    chunk_index: 1,
    metadata: { category: 'language', difficulty: 'intermediate' },
  },
]

export const Route = createFileRoute('/search')({
  component: SearchPage,
})

function SearchPage() {
  const [query, setQuery] = useState('')
  const [searchType, setSearchType] = useState<'text' | 'vector' | 'hybrid'>('hybrid')
  const [results, setResults] = useState<any[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const toast = useToast()

  // 模拟搜索
  const searchMutation = useMutation({
    mutationFn: async (searchQuery: string) => {
      setIsSearching(true)
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000))
      return mockSearchResults.filter(result => 
        result.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        result.content.toLowerCase().includes(searchQuery.toLowerCase())
      )
    },
    onSuccess: (data) => {
      setResults(data)
      setIsSearching(false)
      toast({
        title: `找到 ${data.length} 个结果`,
        status: 'success',
        duration: 2000,
        isClosable: true,
      })
    },
    onError: () => {
      setIsSearching(false)
      toast({
        title: '搜索失败',
        description: '请检查网络连接或稍后重试',
        status: 'error',
        duration: 3000,
        isClosable: true,
      })
    },
  })

  const handleSearch = () => {
    if (!query.trim()) {
      toast({
        title: '请输入搜索关键词',
        status: 'warning',
        duration: 2000,
        isClosable: true,
      })
      return
    }

    searchMutation.mutate(query)
  }

  const getSearchTypeColor = (type: string) => {
    switch (type) {
      case 'text':
        return 'blue'
      case 'vector':
        return 'purple'
      case 'hybrid':
        return 'green'
      default:
        return 'gray'
    }
  }

  const formatScore = (score: number) => {
    return (score * 100).toFixed(1) + '%'
  }

  return (
    <Container maxW="1200px" py={6}>
      <VStack spacing={6} align="stretch">
        {/* 页面标题 */}
        <HStack justify="space-between" align="center">
          <Box>
            <Heading size="xl" mb={2}>
              文档搜索
            </Heading>
            <Text color="gray.600">
              搜索您的知识库中的文档和内容
            </Text>
          </Box>
          <Link to="/">
            <IconButton
              aria-label="返回首页"
              icon={<FiHome />}
              size="lg"
              variant="ghost"
            />
          </Link>
        </HStack>

        {/* 搜索输入区域 */}
        <Box bg="white" p={6} borderRadius="lg" shadow="sm" border="1px" borderColor="gray.200">
          <VStack spacing={4}>
            <HStack w="100%" spacing={4}>
              <Input
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                placeholder="输入搜索关键词..."
                size="lg"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    handleSearch()
                  }
                }}
              />
              <Select
                value={searchType}
                onChange={(e) => setSearchType(e.target.value as 'text' | 'vector' | 'hybrid')}
                w="200px"
                size="lg"
              >
                <option value="text">文本搜索</option>
                <option value="vector">语义搜索</option>
                <option value="hybrid">混合搜索</option>
              </Select>
              <Button
                leftIcon={<FiSearch />}
                colorScheme="blue"
                size="lg"
                onClick={handleSearch}
                isLoading={isSearching}
                disabled={!query.trim()}
              >
                搜索
              </Button>
            </HStack>

            <HStack spacing={4} w="100%" justify="space-between">
              <HStack spacing={2}>
                <Text fontSize="sm" color="gray.600">
                  搜索类型：
                </Text>
                <Badge colorScheme={getSearchTypeColor(searchType)}>
                  {searchType === 'text' && '文本搜索'}
                  {searchType === 'vector' && '语义搜索'}
                  {searchType === 'hybrid' && '混合搜索'}
                </Badge>
              </HStack>
              <Text fontSize="sm" color="gray.500">
                {results.length > 0 && `共找到 ${results.length} 个结果`}
              </Text>
            </HStack>
          </VStack>
        </Box>

        {/* 搜索结果 */}
        {isSearching ? (
          <Flex justify="center" py={8}>
            <VStack spacing={4}>
              <Spinner size="lg" color="blue.500" />
              <Text color="gray.600">正在搜索...</Text>
            </VStack>
          </Flex>
        ) : results.length > 0 ? (
          <VStack spacing={4} align="stretch">
            <Divider />
            <Text fontSize="lg" fontWeight="semibold">
              搜索结果
            </Text>
            {results.map((result, index) => (
              <Box
                key={result.id || index}
                bg="white"
                p={6}
                borderRadius="lg"
                shadow="sm"
                border="1px"
                borderColor="gray.200"
                _hover={{ shadow: 'md' }}
              >
                <VStack align="stretch" spacing={3}>
                  <HStack justify="space-between" align="start">
                    <VStack align="start" spacing={1} flex={1}>
                      <HStack>
                        <FiFile />
                        <Text fontWeight="bold" fontSize="lg">
                          {result.title}
                        </Text>
                        {result.score && (
                          <Badge colorScheme="green" variant="subtle">
                            相关度: {formatScore(result.score)}
                          </Badge>
                        )}
                      </HStack>
                      <Text fontSize="sm" color="gray.500">
                        文档ID: {result.document_id} | 片段: {result.chunk_index}
                      </Text>
                    </VStack>
                    <Button
                      size="sm"
                      variant="ghost"
                      rightIcon={<FiExternalLink />}
                      color="blue.500"
                      _hover={{ color: 'blue.600' }}
                    >
                      查看文档
                    </Button>
                  </HStack>

                  <Box>
                    <Text fontSize="sm" color="gray.700" lineHeight="1.6">
                      {result.content}
                    </Text>
                  </Box>

                  {result.metadata && Object.keys(result.metadata).length > 0 && (
                    <Box>
                      <Text fontSize="xs" color="gray.500" mb={1}>
                        元数据:
                      </Text>
                      <HStack spacing={2} flexWrap="wrap">
                        {Object.entries(result.metadata).map(([key, value]) => (
                          <Badge key={key} variant="outline" fontSize="xs">
                            {key}: {String(value)}
                          </Badge>
                        ))}
                      </HStack>
                    </Box>
                  )}
                </VStack>
              </Box>
            ))}
          </VStack>
        ) : query && !isSearching ? (
          <Box textAlign="center" py={8}>
            <Text color="gray.500" fontSize="lg">
              没有找到相关结果
            </Text>
            <Text color="gray.400" fontSize="sm" mt={2}>
              尝试使用不同的关键词或搜索类型
            </Text>
          </Box>
        ) : (
          <Box textAlign="center" py={8}>
            <Text color="gray.500" fontSize="lg">
              输入关键词开始搜索
            </Text>
            <Text color="gray.400" fontSize="sm" mt={2}>
              支持文本搜索、语义搜索和混合搜索
            </Text>
          </Box>
        )}
      </VStack>
    </Container>
  )
}
