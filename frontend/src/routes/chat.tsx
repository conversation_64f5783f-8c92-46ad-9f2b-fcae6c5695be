import { createFileRoute } from '@tanstack/react-router'
import { useState, useRef, useEffect } from 'react'
import {
  Box,
  VStack,
  HStack,
  Input,
  Button,
  Text,
  Flex,
  useToast,
  Spinner,
  Avatar,
  Divider,
  IconButton,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Container,
  Heading,
} from '@chakra-ui/react'
import { FiSend, FiPlus, FiMoreVertical, FiTrash2, FiHome } from 'react-icons/fi'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { Link } from '@tanstack/react-router'

// 模拟API服务 - 实际项目中会使用生成的客户端
const mockConversations = [
  { id: '1', title: '关于React的问题', created_at: new Date().toISOString() },
  { id: '2', title: 'TypeScript最佳实践', created_at: new Date().toISOString() },
]

const mockMessages = [
  { id: '1', role: 'user', content: '你好，请介绍一下React', created_at: new Date().toISOString() },
  { id: '2', role: 'assistant', content: 'React是一个用于构建用户界面的JavaScript库...', created_at: new Date().toISOString() },
]

export const Route = createFileRoute('/chat')({
  component: ChatPage,
})

function ChatPage() {
  const [message, setMessage] = useState('')
  const [currentConversationId, setCurrentConversationId] = useState<string | null>('1')
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const toast = useToast()
  const queryClient = useQueryClient()

  // 模拟获取对话列表
  const { data: conversations, isLoading: conversationsLoading } = useQuery({
    queryKey: ['conversations'],
    queryFn: () => Promise.resolve({ data: mockConversations }),
  })

  // 模拟获取当前对话的消息
  const { data: messages, isLoading: messagesLoading } = useQuery({
    queryKey: ['conversation-messages', currentConversationId],
    queryFn: () => Promise.resolve({ data: mockMessages }),
    enabled: !!currentConversationId,
  })

  // 模拟创建新对话
  const createConversation = useMutation({
    mutationFn: (title: string) => 
      Promise.resolve({ id: Date.now().toString(), title, created_at: new Date().toISOString() }),
    onSuccess: (data) => {
      setCurrentConversationId(data.id)
      queryClient.invalidateQueries({ queryKey: ['conversations'] })
      toast({
        title: '新对话已创建',
        status: 'success',
        duration: 2000,
        isClosable: true,
      })
    },
  })

  // 模拟发送消息
  const sendMessage = useMutation({
    mutationFn: (messageText: string) => {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve({ 
            id: Date.now().toString(), 
            role: 'assistant', 
            content: `这是对"${messageText}"的回复`, 
            created_at: new Date().toISOString() 
          })
        }, 1000)
      })
    },
    onSuccess: () => {
      setMessage('')
      queryClient.invalidateQueries({
        queryKey: ['conversation-messages', currentConversationId],
      })
      toast({
        title: '消息已发送',
        status: 'success',
        duration: 2000,
        isClosable: true,
      })
    },
  })

  const handleSendMessage = () => {
    if (!message.trim()) return

    if (!currentConversationId) {
      createConversation.mutate(message.slice(0, 50) + '...')
      return
    }

    sendMessage.mutate(message)
  }

  const handleNewChat = () => {
    createConversation.mutate('新对话')
  }

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  return (
    <Container maxW="full" p={0} h="100vh">
      <Flex h="100vh" bg="gray.50">
        {/* 侧边栏 - 对话列表 */}
        <Box w="300px" bg="white" borderRight="1px" borderColor="gray.200" p={4}>
          <VStack spacing={4} align="stretch">
            <HStack justify="space-between">
              <Heading size="md">对话</Heading>
              <Link to="/">
                <IconButton
                  aria-label="返回首页"
                  icon={<FiHome />}
                  size="sm"
                  variant="ghost"
                />
              </Link>
            </HStack>

            <Button
              leftIcon={<FiPlus />}
              colorScheme="blue"
              onClick={handleNewChat}
              isLoading={createConversation.isPending}
            >
              新对话
            </Button>

            <Divider />

            <Text fontSize="sm" fontWeight="bold" color="gray.600">
              对话历史
            </Text>

            {conversationsLoading ? (
              <Spinner />
            ) : (
              <VStack spacing={2} align="stretch">
                {conversations?.data?.map((conv: any) => (
                  <Box
                    key={conv.id}
                    p={3}
                    bg={currentConversationId === conv.id ? 'blue.50' : 'gray.50'}
                    borderRadius="md"
                    cursor="pointer"
                    onClick={() => setCurrentConversationId(conv.id)}
                    _hover={{ bg: 'gray.100' }}
                  >
                    <HStack justify="space-between">
                      <Text fontSize="sm" noOfLines={2}>
                        {conv.title || '未命名对话'}
                      </Text>
                      <Menu>
                        <MenuButton
                          as={IconButton}
                          icon={<FiMoreVertical />}
                          size="sm"
                          variant="ghost"
                        />
                        <MenuList>
                          <MenuItem icon={<FiTrash2 />}>
                            删除
                          </MenuItem>
                        </MenuList>
                      </Menu>
                    </HStack>
                    <Text fontSize="xs" color="gray.500" mt={1}>
                      {new Date(conv.created_at).toLocaleDateString()}
                    </Text>
                  </Box>
                ))}
              </VStack>
            )}
          </VStack>
        </Box>

        {/* 主聊天区域 */}
        <Flex flex={1} direction="column">
          {/* 聊天消息区域 */}
          <Box flex={1} p={4} overflowY="auto">
            {!currentConversationId ? (
              <Flex h="100%" align="center" justify="center">
                <VStack spacing={4}>
                  <Text fontSize="xl" color="gray.500">
                    选择一个对话或创建新对话开始聊天
                  </Text>
                  <Button leftIcon={<FiPlus />} colorScheme="blue" onClick={handleNewChat}>
                    开始新对话
                  </Button>
                </VStack>
              </Flex>
            ) : messagesLoading ? (
              <Flex h="100%" align="center" justify="center">
                <Spinner size="lg" />
              </Flex>
            ) : (
              <VStack spacing={4} align="stretch">
                {messages?.data?.map((msg: any) => (
                  <HStack
                    key={msg.id}
                    align="start"
                    justify={msg.role === 'user' ? 'flex-end' : 'flex-start'}
                  >
                    {msg.role === 'assistant' && (
                      <Avatar size="sm" name="AI" bg="blue.500" />
                    )}
                    <Box
                      maxW="70%"
                      p={3}
                      bg={msg.role === 'user' ? 'blue.500' : 'gray.100'}
                      color={msg.role === 'user' ? 'white' : 'black'}
                      borderRadius="lg"
                    >
                      <Text>{msg.content}</Text>
                      <Text fontSize="xs" opacity={0.7} mt={1}>
                        {new Date(msg.created_at).toLocaleTimeString()}
                      </Text>
                    </Box>
                    {msg.role === 'user' && (
                      <Avatar size="sm" name="User" bg="green.500" />
                    )}
                  </HStack>
                ))}
                <div ref={messagesEndRef} />
              </VStack>
            )}
          </Box>

          {/* 输入区域 */}
          {currentConversationId && (
            <Box p={4} bg="white" borderTop="1px" borderColor="gray.200">
              <HStack spacing={2}>
                <Input
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="输入消息..."
                  onKeyPress={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault()
                      handleSendMessage()
                    }
                  }}
                />
                <Button
                  leftIcon={<FiSend />}
                  colorScheme="blue"
                  onClick={handleSendMessage}
                  isLoading={sendMessage.isPending}
                  disabled={!message.trim()}
                >
                  发送
                </Button>
              </HStack>
            </Box>
          )}
        </Flex>
      </Flex>
    </Container>
  )
}
