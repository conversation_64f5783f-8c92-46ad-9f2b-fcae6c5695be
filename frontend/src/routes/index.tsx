import { createFileRoute, Link } from '@tanstack/react-router'
import {
  Box,
  Container,
  VStack,
  Heading,
  Text,
  SimpleGrid,
  Icon,
  Flex,
} from '@chakra-ui/react'
import { FiMessageCircle, FiSearch, FiBook, FiSettings } from 'react-icons/fi'

export const Route = createFileRoute('/')({
  component: HomePage,
})

function HomePage() {
  const quickActions = [
    {
      title: '智能对话',
      description: '与AI助手进行智能对话，获取知识库中的信息',
      icon: FiMessageCircle,
      href: '/chat',
      color: 'blue',
    },
    {
      title: '文档搜索',
      description: '搜索知识库中的文档和内容',
      icon: FiSearch,
      href: '/search',
      color: 'green',
    },
    {
      title: '文档管理',
      description: '管理您的文档和知识库',
      icon: FiBook,
      href: '/documents',
      color: 'purple',
    },
    {
      title: '系统设置',
      description: '配置系统参数和个人设置',
      icon: FiSettings,
      href: '/settings',
      color: 'orange',
    },
  ]

  return (
    <Container maxW="6xl" py={8}>
      <VStack spacing={8} align="stretch">
        {/* 欢迎区域 */}
        <Box textAlign="center">
          <Heading size="2xl" mb={4}>
            欢迎使用 Master-Know
          </Heading>
          <Text fontSize="xl" color="gray.600">
            您的智能知识管理系统
          </Text>
        </Box>

        {/* 快速操作 */}
        <Box>
          <Heading size="lg" mb={6} textAlign="center">
            快速开始
          </Heading>
          <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6}>
            {quickActions.map((action) => (
              <Link key={action.href} to={action.href}>
                <Box
                  bg="white"
                  p={6}
                  borderRadius="lg"
                  shadow="sm"
                  border="1px"
                  borderColor="gray.200"
                  _hover={{ 
                    shadow: 'md', 
                    transform: 'translateY(-2px)',
                    borderColor: `${action.color}.200`
                  }}
                  transition="all 0.2s"
                  cursor="pointer"
                  h="full"
                >
                  <VStack spacing={4} align="start" h="full">
                    <Flex
                      w={12}
                      h={12}
                      bg={`${action.color}.100`}
                      borderRadius="lg"
                      align="center"
                      justify="center"
                    >
                      <Icon as={action.icon} w={6} h={6} color={`${action.color}.500`} />
                    </Flex>
                    <VStack spacing={2} align="start" flex={1}>
                      <Text fontWeight="bold" fontSize="lg">
                        {action.title}
                      </Text>
                      <Text fontSize="sm" color="gray.600" lineHeight="1.5">
                        {action.description}
                      </Text>
                    </VStack>
                  </VStack>
                </Box>
              </Link>
            ))}
          </SimpleGrid>
        </Box>

        {/* 系统状态 */}
        <Box>
          <Heading size="lg" mb={6} textAlign="center">
            系统状态
          </Heading>
          <SimpleGrid columns={{ base: 1, md: 3 }} spacing={6}>
            <Box bg="white" p={6} borderRadius="lg" shadow="sm" border="1px" borderColor="gray.200" textAlign="center">
              <VStack spacing={2}>
                <Text fontSize="2xl" fontWeight="bold" color="green.500">
                  ✓ 正常
                </Text>
                <Text fontSize="sm" color="gray.600">
                  API服务状态
                </Text>
              </VStack>
            </Box>
            <Box bg="white" p={6} borderRadius="lg" shadow="sm" border="1px" borderColor="gray.200" textAlign="center">
              <VStack spacing={2}>
                <Text fontSize="2xl" fontWeight="bold" color="blue.500">
                  ✓ 在线
                </Text>
                <Text fontSize="sm" color="gray.600">
                  搜索服务状态
                </Text>
              </VStack>
            </Box>
            <Box bg="white" p={6} borderRadius="lg" shadow="sm" border="1px" borderColor="gray.200" textAlign="center">
              <VStack spacing={2}>
                <Text fontSize="2xl" fontWeight="bold" color="purple.500">
                  ✓ 就绪
                </Text>
                <Text fontSize="sm" color="gray.600">
                  AI助手状态
                </Text>
              </VStack>
            </Box>
          </SimpleGrid>
        </Box>
      </VStack>
    </Container>
  )
}
