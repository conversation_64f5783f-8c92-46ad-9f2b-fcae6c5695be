{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "generate-client": "openapi-ts"}, "dependencies": {"@chakra-ui/icons": "^2.2.4", "@chakra-ui/react": "^2.10.9", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@hey-api/client-axios": "^0.9.1", "@tanstack/react-query": "^5.85.3", "@tanstack/react-router": "^1.19.1", "axios": "^1.7.4", "framer-motion": "^10.18.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-icons": "^5.5.0"}, "devDependencies": {"@eslint/js": "^9.33.0", "@hey-api/openapi-ts": "^0.57.1", "@tanstack/router-vite-plugin": "^1.19.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2"}}