#!/usr/bin/env python3
"""
Master-Know 前端功能测试脚本
测试前端应用的基本功能和API连接
"""

import requests
import time
import sys
from typing import Dict, Any

class FrontendTester:
    def __init__(self):
        self.frontend_url = "http://localhost:3000"
        self.backend_url = "http://localhost:8000"
        self.results = []
    
    def log_result(self, test_name: str, success: bool, message: str = ""):
        """记录测试结果"""
        status = "✅ PASS" if success else "❌ FAIL"
        self.results.append({
            "test": test_name,
            "success": success,
            "message": message
        })
        print(f"{status} {test_name}: {message}")
    
    def test_frontend_server(self) -> bool:
        """测试前端服务器是否运行"""
        try:
            response = requests.get(self.frontend_url, timeout=5)
            if response.status_code == 200:
                self.log_result("前端服务器", True, "服务器正常运行")
                return True
            else:
                self.log_result("前端服务器", False, f"状态码: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            self.log_result("前端服务器", False, f"连接失败: {str(e)}")
            return False
    
    def test_backend_api(self) -> bool:
        """测试后端API是否可用"""
        try:
            response = requests.get(f"{self.backend_url}/api/v1/utils/health-check", timeout=5)
            if response.status_code == 200:
                self.log_result("后端API", True, "API服务正常")
                return True
            else:
                self.log_result("后端API", False, f"状态码: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            self.log_result("后端API", False, f"连接失败: {str(e)}")
            return False
    
    def test_openapi_spec(self) -> bool:
        """测试OpenAPI规范是否可获取"""
        try:
            response = requests.get(f"{self.backend_url}/api/v1/openapi.json", timeout=5)
            if response.status_code == 200:
                spec = response.json()
                endpoints = len(spec.get('paths', {}))
                self.log_result("OpenAPI规范", True, f"获取成功，包含 {endpoints} 个端点")
                return True
            else:
                self.log_result("OpenAPI规范", False, f"状态码: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            self.log_result("OpenAPI规范", False, f"获取失败: {str(e)}")
            return False
    
    def test_search_api(self) -> bool:
        """测试搜索API"""
        try:
            payload = {
                "query": "test",
                "search_type": "text",
                "limit": 5
            }
            response = requests.post(
                f"{self.backend_url}/api/v1/search/documents",
                json=payload,
                timeout=10
            )
            if response.status_code == 200:
                results = response.json()
                self.log_result("搜索API", True, f"搜索成功，返回 {len(results.get('results', []))} 个结果")
                return True
            else:
                self.log_result("搜索API", False, f"状态码: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            self.log_result("搜索API", False, f"请求失败: {str(e)}")
            return False
    
    def test_chat_api(self) -> bool:
        """测试聊天API（需要认证，这里只测试端点是否存在）"""
        try:
            # 不带认证的请求应该返回401
            payload = {
                "conversation_id": "test",
                "message": "Hello"
            }
            response = requests.post(
                f"{self.backend_url}/api/v1/conversations/chat",
                json=payload,
                timeout=10
            )
            # 401表示端点存在但需要认证
            if response.status_code in [401, 422]:
                self.log_result("聊天API", True, "端点存在（需要认证）")
                return True
            else:
                self.log_result("聊天API", False, f"意外状态码: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            self.log_result("聊天API", False, f"请求失败: {str(e)}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始测试 Master-Know 前端功能...")
        print("=" * 50)
        
        # 测试前端服务器
        frontend_ok = self.test_frontend_server()
        
        # 测试后端API
        backend_ok = self.test_backend_api()
        
        # 测试OpenAPI规范
        openapi_ok = self.test_openapi_spec()
        
        # 测试搜索API
        search_ok = self.test_search_api()
        
        # 测试聊天API
        chat_ok = self.test_chat_api()
        
        # 汇总结果
        print("\n" + "=" * 50)
        print("📊 测试结果汇总:")
        
        passed = sum(1 for r in self.results if r["success"])
        total = len(self.results)
        
        for result in self.results:
            status = "✅" if result["success"] else "❌"
            print(f"  {status} {result['test']}")
        
        print(f"\n总计: {passed}/{total} 测试通过")
        
        if passed == total:
            print("🎉 所有测试通过！前端应用已准备就绪。")
            print(f"\n🌐 访问地址: {self.frontend_url}")
            print("📋 可用功能:")
            print("  - 首页: 系统概览和快速导航")
            print("  - 聊天: /chat - 智能对话功能")
            print("  - 搜索: /search - 文档搜索功能")
            return True
        else:
            print("⚠️  部分测试失败，请检查相关服务。")
            return False

def main():
    """主函数"""
    tester = FrontendTester()
    success = tester.run_all_tests()
    
    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main()
