#!/usr/bin/env python3
"""
Master-Know 前端最终测试脚本
验证前端应用的完整功能
"""

import requests
import time
import sys
import subprocess
import os
from typing import Dict, Any

class FinalTester:
    def __init__(self):
        self.frontend_url = "http://localhost:3000"
        self.backend_url = "http://localhost:8000"
        self.results = []
    
    def log_result(self, test_name: str, success: bool, message: str = ""):
        """记录测试结果"""
        status = "✅ PASS" if success else "❌ FAIL"
        self.results.append({
            "test": test_name,
            "success": success,
            "message": message
        })
        print(f"{status} {test_name}: {message}")
    
    def test_build_success(self) -> bool:
        """测试构建是否成功"""
        try:
            # 检查dist目录是否存在
            if os.path.exists("dist") and os.path.exists("dist/index.html"):
                self.log_result("构建成功", True, "dist目录和index.html存在")
                return True
            else:
                self.log_result("构建成功", False, "dist目录或index.html不存在")
                return False
        except Exception as e:
            self.log_result("构建成功", False, f"检查失败: {str(e)}")
            return False
    
    def test_frontend_server(self) -> bool:
        """测试前端服务器是否运行"""
        try:
            response = requests.get(self.frontend_url, timeout=5)
            if response.status_code == 200:
                self.log_result("前端服务器", True, "服务器正常运行")
                return True
            else:
                self.log_result("前端服务器", False, f"状态码: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            self.log_result("前端服务器", False, f"连接失败: {str(e)}")
            return False
    
    def test_routes_accessible(self) -> bool:
        """测试路由是否可访问"""
        routes = [
            ("/", "首页"),
            ("/chat", "聊天页面"),
            ("/search", "搜索页面"),
        ]
        
        all_success = True
        for route, name in routes:
            try:
                response = requests.get(f"{self.frontend_url}{route}", timeout=5)
                if response.status_code == 200:
                    self.log_result(f"路由 {name}", True, f"{route} 可访问")
                else:
                    self.log_result(f"路由 {name}", False, f"{route} 状态码: {response.status_code}")
                    all_success = False
            except requests.exceptions.RequestException as e:
                self.log_result(f"路由 {name}", False, f"{route} 连接失败: {str(e)}")
                all_success = False
        
        return all_success
    
    def test_backend_api(self) -> bool:
        """测试后端API是否可用"""
        try:
            response = requests.get(f"{self.backend_url}/api/v1/utils/health-check", timeout=5)
            if response.status_code == 200:
                self.log_result("后端API", True, "API服务正常")
                return True
            else:
                self.log_result("后端API", False, f"状态码: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            self.log_result("后端API", False, f"连接失败: {str(e)}")
            return False
    
    def test_static_assets(self) -> bool:
        """测试静态资源是否正确加载"""
        try:
            # 获取首页HTML
            response = requests.get(self.frontend_url, timeout=5)
            if response.status_code == 200:
                html_content = response.text
                # 检查是否包含React应用的基本元素
                if 'id="root"' in html_content:
                    self.log_result("静态资源", True, "HTML结构正确")
                    return True
                else:
                    self.log_result("静态资源", False, "HTML结构不正确")
                    return False
            else:
                self.log_result("静态资源", False, f"无法获取HTML: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            self.log_result("静态资源", False, f"请求失败: {str(e)}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始 Master-Know 前端最终测试...")
        print("=" * 60)
        
        # 测试构建成功
        build_ok = self.test_build_success()
        
        # 测试前端服务器
        frontend_ok = self.test_frontend_server()
        
        # 测试路由可访问性
        routes_ok = self.test_routes_accessible()
        
        # 测试静态资源
        assets_ok = self.test_static_assets()
        
        # 测试后端API
        backend_ok = self.test_backend_api()
        
        # 汇总结果
        print("\n" + "=" * 60)
        print("📊 最终测试结果:")
        
        passed = sum(1 for r in self.results if r["success"])
        total = len(self.results)
        
        for result in self.results:
            status = "✅" if result["success"] else "❌"
            print(f"  {status} {result['test']}")
        
        print(f"\n总计: {passed}/{total} 测试通过")
        
        if passed == total:
            print("\n🎉 所有测试通过！Master-Know 前端应用已完全就绪！")
            print(f"\n🌐 访问地址: {self.frontend_url}")
            print("\n📋 功能清单:")
            print("  ✅ 响应式首页 - 系统概览和快速导航")
            print("  ✅ 智能聊天 - /chat - 模拟AI对话功能")
            print("  ✅ 文档搜索 - /search - 模拟搜索功能")
            print("  ✅ 现代化UI - Chakra UI v2组件库")
            print("  ✅ 路由系统 - TanStack Router")
            print("  ✅ 状态管理 - TanStack Query")
            print("  ✅ 生产构建 - 已优化的静态资源")
            
            print("\n🔧 技术栈:")
            print("  - React 19 + TypeScript")
            print("  - Chakra UI v2 (稳定版本)")
            print("  - TanStack Router + Query")
            print("  - Vite 构建工具")
            print("  - 模块化组件架构")
            
            print("\n🚀 下一步:")
            print("  1. 集成真实的API客户端")
            print("  2. 添加用户认证功能")
            print("  3. 完善错误处理和加载状态")
            print("  4. 添加更多交互功能")
            
            return True
        else:
            print("⚠️  部分测试失败，请检查相关服务。")
            return False

def main():
    """主函数"""
    tester = FinalTester()
    success = tester.run_all_tests()
    
    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main()
