# 🚀 Master-Know 前端开发 - 下一步执行指令

## 📋 当前状态
- ✅ 后端API已验证可用 (19/21端点正常)
- ✅ 前端已回退到干净状态
- ✅ 技术方案已确定
- ⏳ 准备开始前端开发

## 🎯 立即执行的命令

### 1. 环境准备
```bash
cd frontend

# 安装正确的Chakra UI v2版本
npm install @chakra-ui/react@^2.8.2 @chakra-ui/icons@^2.1.1
npm install @emotion/react@^11.11.0 @emotion/styled@^11.11.0  
npm install framer-motion@^10.16.16

# 验证安装
npm list @chakra-ui/react
```

### 2. 重新生成API客户端
```bash
# 获取最新OpenAPI规范
curl -o openapi.json http://localhost:8000/api/v1/openapi.json

# 生成客户端代码
npm run generate-client
```

### 3. 创建组件目录结构
```bash
mkdir -p src/components/Chat
mkdir -p src/components/Search  
mkdir -p src/components/Dashboard
```

### 4. 启动开发服务器
```bash
npm run dev
```

## 🎨 开发优先级

### 第一阶段：核心功能 (优先级最高)
1. **ChatInterface组件**
   - 基础聊天界面
   - 消息发送/接收
   - 对话管理

2. **SearchInterface组件**  
   - 全文搜索功能
   - 语义搜索功能
   - 搜索结果展示

3. **Dashboard更新**
   - 添加聊天和搜索入口
   - 简化现有复杂组件

### 第二阶段：完善功能
1. 路由配置
2. 错误处理
3. 加载状态
4. 响应式设计

## 🔧 关键技术要点

### Chakra UI v2 API使用
```tsx
// ✅ 正确的v2语法
<Button leftIcon={<FiPlus />} colorScheme="blue">
  New Chat
</Button>

<VStack spacing={4}>
  {/* 内容 */}
</VStack>

// Toast使用
const toast = useToast()
toast({
  title: "Success",
  description: "Message sent",
  status: "success",
  duration: 3000,
  isClosable: true
})
```

### API调用模式
```tsx
// 使用TanStack Query
const { data, isLoading } = useQuery({
  queryKey: ["conversations"],
  queryFn: () => ConversationsService.readConversations({ limit: 50 })
})

// 使用Mutation
const sendMessage = useMutation({
  mutationFn: (message: string) => ConversationsService.chatWithAi({
    requestBody: { conversation_id: id, message }
  }),
  onSuccess: () => {
    // 处理成功
  }
})
```

## 🧪 测试验证

### 功能测试脚本
```bash
# 运行现有的API测试
python3 test_frontend_functionality.py

# 检查构建
npm run build

# 预览构建结果
npm run preview
```

### 手动测试清单
- [ ] 登录功能正常
- [ ] 聊天界面可访问
- [ ] 搜索界面可访问  
- [ ] API调用成功
- [ ] 响应式设计正常

## 📝 开发注意事项

### 避免的错误
1. **不要使用Chakra UI v3 API** - 会导致大量TypeScript错误
2. **不要修改现有复杂组件** - 专注于新功能开发
3. **不要跳过类型检查** - 确保TypeScript编译通过

### 推荐的开发流程
1. 先创建基础组件结构
2. 实现核心功能逻辑
3. 添加样式和交互
4. 测试和优化
5. 集成到路由系统

## 🎯 成功标准

### 最小可行产品 (MVP)
- [ ] 用户可以登录
- [ ] 用户可以发送聊天消息
- [ ] 用户可以搜索文档
- [ ] 基础导航正常工作
- [ ] 没有TypeScript编译错误

### 完整功能
- [ ] 对话历史管理
- [ ] 搜索结果分页
- [ ] 错误处理完善
- [ ] 加载状态优化
- [ ] 响应式设计完整

## 🚀 准备开始！

**现在可以开始新的对话窗口，按照以上指令执行前端开发。**

**关键文件已准备：**
- `FRONTEND_DEVELOPMENT_PLAN.md` - 完整技术方案
- `NEXT_STEPS_INSTRUCTIONS.md` - 执行指令
- `test_frontend_functionality.py` - API测试脚本

**后端服务状态：**
- ✅ 所有服务正常运行
- ✅ API端点已验证
- ✅ 认证系统正常

**开始开发吧！** 🎉
